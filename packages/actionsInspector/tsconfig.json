{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "../ma-next/netlify/functions/_agents/nangoConstants.ts", "../ma-next/netlify/functions/_tools/actions/google-mail/search-messages.ts", "../ma-next/netlify/functions/_tools/actions/models.ts", "../ma-next/netlify/functions/_tools/actions/twitter-v2/get-user-profile.ts", "../ma-next/netlify/functions/_tools/actions/twitter-v2/send-post.ts"]}