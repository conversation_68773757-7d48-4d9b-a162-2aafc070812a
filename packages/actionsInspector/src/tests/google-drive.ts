import { executeProviderActions } from './providerRunner';

export async function runGoogleDriveTest() {
  const results = await executeProviderActions('google-drive', [
    { actionKey: 'list-root-folders', params: {} },
  ]);

  const folderId = (results[0].output as any)?.folders?.[0]?.id;

  results.push(
    ...(await executeProviderActions('google-drive', [
      { actionKey: 'list-documents', params: folderId ? { folderId } : {} },
    ]))
  );

  return results;
}
