import { supabase } from '../lib/supabase';
import fs from 'fs/promises';
import { getRunner } from '../../../ma-next/netlify/functions/_tools/actions';
import { getPseudoNangoAction } from '../../../ma-next/netlify/functions/_nango/getPseudoNangoAction';
import { ACTION_OUTPUT_MODELS_ZOD, ACTION_OUTPUTS } from '../../../emcpe-server/src/constants';

async function getConnectionId(providerKey: string): Promise<string | null> {
  const { data } = await supabase
    .from('connections')
    .select('id')
    .eq('providerKey', providerKey)
    .limit(1)
    .maybeSingle();
  return data?.id || null;
}

export async function executeProviderActions(
  providerKey: string,
  steps: { actionKey: string; params: Record<string, any>; filename?: string }[]
) {
  const connectionId = await getConnectionId(providerKey);
  if (!connectionId) throw new Error(`No ${providerKey} connection found`);

  await fs.mkdir(`packages/actionsInspector/data/${providerKey}`, { recursive: true });
  const nango = getPseudoNangoAction(providerKey, connectionId);
  const results: any[] = [];

  for (const step of steps) {
    const runner = getRunner(providerKey, step.actionKey);
    if (!runner) throw new Error(`No handler for ${providerKey}:${step.actionKey}`);
    const output = await runner(nango, step.params);
    const modelName = ACTION_OUTPUTS.find(
      (o) => o.provider === providerKey && o.action === step.actionKey
    )?.model;
    const valid = modelName
      ? ACTION_OUTPUT_MODELS_ZOD[modelName as keyof typeof ACTION_OUTPUT_MODELS_ZOD]?.safeParse(output).success ?? true
      : true;
    const file = `result-data/${providerKey}/${step.filename || step.actionKey}.json`;
    await fs.writeFile(file, JSON.stringify(output, null, 2));
    results.push({ action: step.actionKey, input: step.params, output, valid });
  }

  return results;
}
