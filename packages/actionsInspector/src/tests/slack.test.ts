process.env.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost';
process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || 'dummy';

async function main() {
  const { runSlackTest } = await import('./slack');
  if (typeof runSlackTest !== 'function') {
    throw new Error('runSlackTest should be a function');
  }
  console.log('slack test stub passed');
}

main();
