import { useState } from 'react';
import { ChevronRight, Play, Settings } from 'lucide-react';
import { ActionDefinition, ProviderGroup } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { cn } from '@/lib/utils';

interface ProviderListProps {
  providers: ProviderGroup[];
  selectedAction: { provider: string; action: string } | null;
  onActionSelect: (provider: string, action: string) => void;
  onRunAction: (provider: string, action: string) => void;
}

export function ProviderList({ 
  providers, 
  selectedAction, 
  onActionSelect, 
  onRunAction 
}: ProviderListProps) {
  const [expandedProviders, setExpandedProviders] = useState<string[]>([]);

  const toggleProvider = (provider: string) => {
    setExpandedProviders(prev => 
      prev.includes(provider) 
        ? prev.filter(p => p !== provider)
        : [...prev, provider]
    );
  };

  const isActionSelected = (provider: string, action: string) => {
    return selectedAction?.provider === provider && selectedAction?.action === action;
  };

  const getProviderIcon = (providerName: string) => {
    // You can customize icons based on provider
    switch (providerName) {
      case 'slack':
        return '💬';
      case 'github':
        return '🐙';
      case 'google-calendar':
      case 'google-mail':
      case 'google-docs':
      case 'google-drive':
      case 'google-sheet':
        return '🔍';
      case 'notion':
        return '📝';
      case 'linear':
        return '📋';
      case 'harvest':
        return '⏰';
      case 'linkedin':
        return '💼';
      case 'twitter-v2':
        return '🐦';
      default:
        return '⚙️';
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Providers</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Accordion 
          type="multiple" 
          value={expandedProviders} 
          onValueChange={setExpandedProviders}
          className="w-full"
        >
          {providers.map((provider) => (
            <AccordionItem key={provider.name} value={provider.name} className="border-b-0">
              <AccordionTrigger className="px-6 py-3 hover:bg-muted/50">
                <div className="flex items-center space-x-3">
                  <span className="text-lg">{getProviderIcon(provider.name)}</span>
                  <div className="text-left">
                    <div className="font-medium">{provider.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {provider.actions.length} action{provider.actions.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pb-0">
                <div className="space-y-1">
                  {provider.actions.map((action) => (
                    <div
                      key={action.action}
                      className={cn(
                        "flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent",
                        isActionSelected(provider.name, action.action) && "bg-muted border-l-primary"
                      )}
                      onClick={() => onActionSelect(provider.name, action.action)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">
                          {action.action}
                        </div>
                        {action.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {action.description}
                          </div>
                        )}
                        {action.model && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 truncate">
                            → {action.model}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-8 w-8 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          onRunAction(provider.name, action.action);
                        }}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}
