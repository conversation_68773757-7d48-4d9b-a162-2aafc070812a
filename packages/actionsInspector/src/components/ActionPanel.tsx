import { useState } from 'react';
import { Play, Code, FileText, Loader2 } from 'lucide-react';
import { ActionDefinition } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { useThemeContext } from '@/components/ThemeProvider';

interface ActionPanelProps {
  action: ActionDefinition | null;
  onExecute: (provider: string, action: string, params: Record<string, any>) => Promise<void>;
  isExecuting: boolean;
  lastResult: any;
}

export function ActionPanel({ action, onExecute, isExecuting, lastResult }: ActionPanelProps) {
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const { theme } = useThemeContext();

  if (!action) {
    return (
      <Card className="h-full">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center h-full">
          <Code className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Select an Action</h3>
          <p className="text-muted-foreground">
            Choose an action from the provider list to view its details and execute it.
          </p>
        </CardContent>
      </Card>
    );
  }

  const handleParameterChange = (key: string, value: any) => {
    setParameters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleExecute = async () => {
    await onExecute(action.provider, action.action, parameters);
  };

  const getCommonParameters = (provider: string, actionName: string) => {
    // Define common parameters for different actions
    const commonParams: Record<string, string[]> = {
      'slack': ['channel', 'text', 'user', 'message_ts'],
      'github': ['owner', 'repo', 'issue_number', 'title', 'body'],
      'google-calendar': ['summary', 'start', 'end', 'description'],
      'google-mail': ['to', 'subject', 'body', 'query'],
      'notion': ['query', 'page_id', 'database_id'],
      'linear': ['title', 'description', 'team_id', 'project_id'],
      'harvest': ['project_id', 'task_id', 'hours', 'notes'],
      'google-drive': ['folderId', 'name', 'mimeType'],
      'google-docs': ['title', 'content'],
      'google-sheet': ['title', 'data'],
      'linkedin': ['text', 'visibility'],
      'twitter-v2': ['text']
    };

    return commonParams[provider] || [];
  };

  const renderParameterInput = (paramName: string) => {
    const value = parameters[paramName] || '';

    if (paramName.includes('body') || paramName.includes('content') || paramName.includes('description')) {
      return (
        <Textarea
          placeholder={`Enter ${paramName}...`}
          value={value}
          onChange={(e) => handleParameterChange(paramName, e.target.value)}
          className="min-h-[100px]"
        />
      );
    }

    return (
      <Input
        placeholder={`Enter ${paramName}...`}
        value={value}
        onChange={(e) => handleParameterChange(paramName, e.target.value)}
      />
    );
  };

  const commonParams = getCommonParameters(action.provider, action.action);

  return (
    <div className="space-y-6">
      {/* Action Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{action.provider}:{action.action}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {action.description && (
            <p className="text-sm text-muted-foreground">{action.description}</p>
          )}

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Provider:</span>
              <span className="ml-2 text-muted-foreground">{action.provider}</span>
            </div>
            <div>
              <span className="font-medium">Output Model:</span>
              <span className="ml-2 text-muted-foreground">{action.model || 'None'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Parameters */}
      <Card>
        <CardHeader>
          <CardTitle>Parameters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {commonParams.length > 0 ? (
            commonParams.map((param) => (
              <div key={param} className="space-y-2">
                <label className="text-sm font-medium">{param}</label>
                {renderParameterInput(param)}
              </div>
            ))
          ) : (
            <div className="text-sm text-muted-foreground">
              No parameters required for this action.
            </div>
          )}

          <Separator />

          <div className="space-y-2">
            <label className="text-sm font-medium">Custom JSON Parameters</label>
            <Textarea
              placeholder='{"key": "value"}'
              value={JSON.stringify(parameters, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  setParameters(parsed);
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              className="min-h-[100px] font-mono text-xs"
            />
          </div>

          <Button
            onClick={handleExecute}
            disabled={isExecuting}
            className="w-full"
          >
            {isExecuting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Executing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                Execute Action
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle>Last Result</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-96">
              {JSON.stringify(lastResult, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
