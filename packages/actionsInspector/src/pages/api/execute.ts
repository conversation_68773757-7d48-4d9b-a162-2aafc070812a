import type { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../lib/supabase';
import { ACTION_OUTPUTS } from '../../lib/actionData';

async function getConnectionId(providerKey: string): Promise<string | null> {
  const { data } = await supabase
    .from('connections')
    .select('id')
    .eq('providerKey', providerKey)
    .limit(1)
    .maybeSingle();
  return data?.id || null;
}

// Mock implementations for now - these would be replaced with actual imports
function getRunner(provider: string, action: string): Function | undefined {
  // Mock runner that returns sample data
  return async (nango: any, params: any) => {
    return {
      mockResult: true,
      provider,
      action,
      parameters: params,
      timestamp: new Date().toISOString()
    };
  };
}

function getPseudoNangoAction(provider: string, connectionId: string): any {
  return {
    provider,
    connectionId,
    // Mock Nango action object
  };
}

interface ExecuteRequest {
  provider: string;
  action: string;
  parameters: Record<string, any>;
}

interface ExecuteResponse {
  success: boolean;
  result?: any;
  error?: string;
  valid?: boolean;
  model?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ExecuteResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    const { provider, action, parameters }: ExecuteRequest = req.body;

    if (!provider || !action) {
      return res.status(400).json({
        success: false,
        error: 'Provider and action are required'
      });
    }

    // Get connection ID for the provider
    const connectionId = await getConnectionId(provider);
    if (!connectionId) {
      return res.status(400).json({
        success: false,
        error: `No ${provider} connection found`
      });
    }

    // Get the action runner
    const runner = getRunner(provider, action);
    if (!runner) {
      return res.status(404).json({
        success: false,
        error: `No handler found for ${provider}:${action}`
      });
    }

    // Create pseudo Nango action instance
    const nango = getPseudoNangoAction(provider, connectionId);

    // Execute the action
    const result = await runner(nango, parameters || {});

    // Find the expected model for validation
    const actionDef = ACTION_OUTPUTS.find(
      (a) => a.provider === provider && a.action === action
    );

    // For now, we'll assume the result is valid
    // In a real implementation, you'd validate against the Zod schema
    const valid = true;

    res.status(200).json({
      success: true,
      result,
      valid,
      model: actionDef?.model || undefined
    });

  } catch (error: any) {
    console.error('Action execution error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
}
