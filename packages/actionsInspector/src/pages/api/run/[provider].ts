import type { NextApiRequest, NextApiResponse } from 'next';
import { runSlackTest } from '../../../tests/slack';
import { runGoogleDriveTest } from '../../../tests/google-drive';

const runners: Record<string, () => Promise<any[]>> = {
  slack: runSlackTest,
  'google-drive': runGoogleDriveTest,
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') return res.status(405).end();
  const { provider } = req.query;
  const runner = typeof provider === 'string' ? runners[provider] : undefined;
  if (!runner) return res.status(404).json({ error: 'unknown provider' });
  try {
    const results = await runner();
    res.status(200).json({ results });
  } catch (err: any) {
    res.status(500).json({ error: err.message });
  }
}
