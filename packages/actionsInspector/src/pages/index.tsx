import { useState } from 'react';
import { Activity } from 'lucide-react';
import { groupActionsByProvider, getActionDefinition } from '@/lib/actionData';
import { ProviderList } from '@/components/ProviderList';
import { ActionPanel } from '@/components/ActionPanel';
import { ThemeToggle } from '@/components/ThemeToggle';
import { ThemeProvider } from '@/components/ThemeProvider';

function ActionsInspectorContent() {
  const [providers] = useState(() => groupActionsByProvider());
  const [selectedAction, setSelectedAction] = useState<{ provider: string; action: string } | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);

  const isLocal = typeof window === 'undefined' ? true :
    window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  const handleActionSelect = (provider: string, action: string) => {
    setSelectedAction({ provider, action });
    setLastResult(null);
  };

  const handleRunAction = async (provider: string, action: string) => {
    setSelectedAction({ provider, action });
    await executeAction(provider, action, {});
  };

  const executeAction = async (provider: string, action: string, parameters: Record<string, any>) => {
    setIsExecuting(true);
    try {
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider,
          action,
          parameters,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setLastResult({
          success: true,
          result: data.result,
          valid: data.valid,
          model: data.model,
          timestamp: new Date().toISOString(),
        });
      } else {
        setLastResult({
          success: false,
          error: data.error,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      setLastResult({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsExecuting(false);
    }
  };

  if (!isLocal) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <p className="text-muted-foreground">Actions Inspector is only available on localhost.</p>
      </div>
    );
  }

  const currentAction = selectedAction
    ? getActionDefinition(selectedAction.provider, selectedAction.action) || null
    : null;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0">
            <ThemeToggle />
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Nango Actions Inspector</span>
          </h1>
          <p className="text-muted-foreground">Test and debug Nango action endpoints locally</p>
          <p className="text-xs text-muted-foreground">
            {providers.reduce((total, provider) => total + provider.actions.length, 0)} actions across {providers.length} providers
          </p>
        </header>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Left Column - Provider List */}
          <div className="lg:col-span-1">
            <ProviderList
              providers={providers}
              selectedAction={selectedAction}
              onActionSelect={handleActionSelect}
              onRunAction={handleRunAction}
            />
          </div>

          {/* Right Column - Action Panel */}
          <div className="lg:col-span-2 overflow-auto">
            <ActionPanel
              action={currentAction}
              onExecute={executeAction}
              isExecuting={isExecuting}
              lastResult={lastResult}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <ThemeProvider>
      <ActionsInspectorContent />
    </ThemeProvider>
  );
}
