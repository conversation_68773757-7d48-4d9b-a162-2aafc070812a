import { useState } from 'react';

interface LogEntry {
  action: string;
  input: any;
  output: any;
  valid: boolean;
}

export default function Home() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [running, setRunning] = useState(false);
  const isLocal = typeof window === 'undefined' ? true : window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  const runTests = async (provider: string) => {
    setRunning(true);
    const res = await fetch(`/api/run/${provider}`);
    const data = await res.json();
    setLogs(data.results || []);
    setRunning(false);
  };

  if (!isLocal) {
    return <p>Actions Inspector is only available on localhost.</p>;
  }

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold">Actions Inspector</h1>
      <div className="space-x-2">
        <button onClick={() => runTests('slack')} className="px-3 py-2 bg-blue-600 text-white rounded" disabled={running}>
          {running ? 'Running...' : 'Run Slack Tests'}
        </button>
        <button onClick={() => runTests('google-drive')} className="px-3 py-2 bg-green-600 text-white rounded" disabled={running}>
          {running ? 'Running...' : 'Run Drive Tests'}
        </button>
      </div>
      <div className="space-y-4">
        {logs.map((log, idx) => (
          <pre key={idx} className="bg-gray-100 p-2 text-xs overflow-auto">
{JSON.stringify(log, null, 2)}
          </pre>
        ))}
      </div>
    </div>
  );
}
