import { test } from 'node:test';
import { strictEqual, throws } from 'node:assert';
import { EMAIL_WORKFLOW_SCHEMA, A_TO_B_INSTANCE_SCHEMA } from '../_agents/sampleWorkflows';
import { validateTaskflowSchema } from './schemaValidator';

// Successful validation of sample workflow
test('validate email workflow schema', () => {
  const result = validateTaskflowSchema({
    nodes: [
      {
        id: 'node1',
        type: 'agent.aToB',
        parameters: {
          output: { actionKey: 'create-event', providerKey: 'google-calendar' },
          prompt: 'Input data: {{ JSON.stringify(trigger) }}',
          system:
            "You are an assistant that copies new Google Calendar events from the source calendar to the target calendar. Map the event fields from the trigger to the create-event action. Use the event's summary, description, location, start, end, timeZone, and attendees if present. Do not copy event IDs or metadata fields. The target calendarId is '<EMAIL>'.",
          userDeterminedParameters: {
            calendarId:
              '<EMAIL>',
          },
        },
      },
    ],
    steps: [
      {
        type: 'connectProvider',
        provider: 'google-calendar',
        completed: false,
        syncScopes: { events: null },
      },
    ],
    triggers: [
      {
        id: 'trigger1',
        type: 'trigger.syncTrigger',
        condition: {},
        parameters: {
          label: 'New Event on Primary Calendar',
          model: 'GoogleCalendarEvent',
          syncKey: 'events',
          description: 'Triggers when a new event is created on the primary calendar',
          providerKey: 'google-calendar',
          userDeterminedParameters: { calendarId: 'primary' },
        },
      },
    ],
  });
  strictEqual(result, true);
});

// Successful validation of sample workflow
test('validate email workflow schema', () => {
  const result = validateTaskflowSchema(EMAIL_WORKFLOW_SCHEMA);
  strictEqual(result, true);
});

// Failure when referencing unknown trigger property
test('fails on bad template reference', () => {
  const bad = JSON.parse(JSON.stringify(EMAIL_WORKFLOW_SCHEMA));
  bad.nodes[1].parameters.prompt = '{{trigger.missing}}';
  throws(() => validateTaskflowSchema(bad));
});

// Successful validation of aToB workflow
test('validate aToB workflow schema', () => {
  const good = JSON.parse(JSON.stringify(A_TO_B_INSTANCE_SCHEMA));
  good.nodes[0].parameters.providerKey = 'google-mail';
  good.nodes[0].parameters.syncKey = 'emails-fork';
  good.nodes[0].parameters.model = 'GmailEmail';
  const result = validateTaskflowSchema(good);
  strictEqual(result, true);
});

// Failure when userDeterminedParameters invalid
test('fails when aToB user parameters invalid', () => {
  const bad = JSON.parse(JSON.stringify(A_TO_B_INSTANCE_SCHEMA));
  bad.nodes[1].parameters.userDeterminedParameters = { owner: 123 };
  throws(() => validateTaskflowSchema(bad));
});
